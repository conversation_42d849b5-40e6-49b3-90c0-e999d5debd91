import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { createPortal } from 'react-dom'
import { useAppStore } from '../store'
import { useNetworkStore } from '../stores/networkStore'
import {
  enhanceModelInfo,
  searchModels,
  sortModels,
  modelPresets,
  formatPricing,
  formatContextLength
} from '../utils/modelUtils'
import { OpenRouterModel } from '../types'

interface ChatSettingsDrawerProps {
  isOpen: boolean
  onClose: () => void
}

const ChatSettingsDrawer: React.FC<ChatSettingsDrawerProps> = ({ isOpen, onClose }) => {
  const { settings, updateSettings, models } = useAppStore()
  const { isPrivateMode, localModels } = useNetworkStore()
  const [localSettings, setLocalSettings] = useState(settings)
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid')
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [selectedPreset, setSelectedPreset] = useState('Balanced')
  const [isModelsLoaded, setIsModelsLoaded] = useState(false)
  const [displayedModels, setDisplayedModels] = useState<OpenRouterModel[]>([])
  const [loadingMore, setLoadingMore] = useState(false)
  const [showLocalModelToast, setShowLocalModelToast] = useState(false)

  useEffect(() => {
    setLocalSettings(settings)
  }, [settings])

  // Lazy load models when component opens
  useEffect(() => {
    if (isOpen && !isModelsLoaded) {
      const timer = setTimeout(() => {
        setIsModelsLoaded(true)
      }, 100) // Small delay to improve perceived performance
      return () => clearTimeout(timer)
    }
  }, [isOpen, isModelsLoaded])

  // Check for local models and show guidance
  useEffect(() => {
    if (isOpen && isPrivateMode && localModels.length === 0) {
      const timer = setTimeout(() => {
        setShowLocalModelToast(true)
      }, 1000) // Show after 1 second
      return () => clearTimeout(timer)
    }
  }, [isOpen, isPrivateMode, localModels.length])

  // Get available models based on private mode (memoized for performance)
  const availableModels = useMemo(() => {
    if (!isModelsLoaded) return []

    // Transform local models to OpenRouterModel format
    const transformedLocalModels = localModels.map(localModel => ({
      id: localModel.id,
      name: localModel.name,
      description: `Local ${localModel.provider} model`,
      pricing: { prompt: "0", completion: "0" },
      context_length: 4096,
      architecture: { modality: "text", tokenizer: "unknown", instruct_type: undefined },
      top_provider: { max_completion_tokens: undefined },
      per_request_limits: undefined
    })) as OpenRouterModel[]

    if (isPrivateMode) {
      // In private mode, only show local models
      return transformedLocalModels
    }

    // In non-private mode, show both external and local models
    return [...models, ...transformedLocalModels]
  }, [isPrivateMode, localModels, models, isModelsLoaded])

  // Filter models based on search and active filters (optimized)
  const filteredModels = useMemo(() => {
    if (!isModelsLoaded) return []

    let filtered = availableModels

    // Apply search first (most selective)
    if (searchQuery.trim()) {
      filtered = searchModels(filtered, searchQuery)
    }

    // Apply active filters
    if (activeFilters.length > 0) {
      filtered = filtered.filter(model => {
        const enhanced = enhanceModelInfo(model)
        return activeFilters.some(filter => {
          switch (filter) {
            case 'flagship': return enhanced.isFlagship
            case 'free': return enhanced.isFree
            case 'reasoning': return enhanced.isReasoning
            case 'vision': return enhanced.isVision
            case 'local': return isPrivateMode
            case 'favorites': return localSettings.favoriteModels?.includes(model.id)
            default: return true
          }
        })
      })
    }

    return sortModels(filtered, 'name')
  }, [availableModels, searchQuery, activeFilters, localSettings.favoriteModels, isPrivateMode, isModelsLoaded])

  // Paginated models for better performance
  const paginatedModels = useMemo(() => {
    const itemsPerPage = viewMode === 'grid' ? 20 : 50
    return filteredModels.slice(0, itemsPerPage)
  }, [filteredModels, viewMode])

  // Helper functions (memoized for performance)
  const toggleFavorite = useCallback((modelId: string) => {
    const favorites = localSettings.favoriteModels || []
    const newFavorites = favorites.includes(modelId)
      ? favorites.filter(id => id !== modelId)
      : [...favorites, modelId]
    setLocalSettings({ ...localSettings, favoriteModels: newFavorites })
  }, [localSettings])

  const toggleFilter = useCallback((filter: string) => {
    setActiveFilters(prev =>
      prev.includes(filter)
        ? prev.filter(f => f !== filter)
        : [...prev, filter]
    )
  }, [])

  const applyPreset = (preset: typeof modelPresets[0]) => {
    setLocalSettings({
      ...localSettings,
      temperature: preset.temperature,
      topP: preset.topP,
      topK: preset.topK,
      maxTokens: preset.maxTokens
    })
    setSelectedPreset(preset.name)
  }

  const handleSave = async () => {
    try {
      // Update settings in store and save to electron
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }
      
      onClose()
    } catch (error) {
      console.error('Failed to save chat settings:', error)
    }
  }

  if (!isOpen) return null

  const modalContent = (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4"
      onClick={onClose}
    >
      <div
        className="bg-gray-800 border border-gray-700 rounded-lg w-full max-w-7xl max-h-[95vh] overflow-hidden shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-medium text-supplement1">Chat Settings</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors"
          >
            <i className="fa-solid fa-xmark text-sm"></i>
          </button>
        </div>

        {/* Main Content: 2 Columns */}
        <div className="flex h-[calc(95vh-120px)]">
          
          {/* Left Column: Model Selection */}
          <div className="w-3/5 border-r border-gray-700 flex flex-col">
            
            {/* Search Bar */}
            <div className="p-3 border-b border-gray-700 bg-gray-800/50">
              <div className="flex items-center gap-3">
                <div className="relative flex-1">
                  <i className="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs"></i>
                  <input
                    type="text"
                    placeholder="Search models..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-9 pr-3 py-2 text-sm bg-gray-700 border border-gray-600 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div className="flex items-center gap-1 bg-gray-700 rounded-md p-1">
                  <span className="text-xs text-gray-400 px-2">View:</span>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`px-2 py-1 text-xs rounded transition-colors ${
                      viewMode === 'list'
                        ? 'bg-primary text-gray-900 font-medium'
                        : 'text-gray-400 hover:text-white hover:bg-gray-600'
                    }`}
                    title="List View"
                  >
                    <i className="fa-solid fa-list mr-1"></i>List
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`px-2 py-1 text-xs rounded transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-primary text-gray-900 font-medium'
                        : 'text-gray-400 hover:text-white hover:bg-gray-600'
                    }`}
                    title="Grid View"
                  >
                    <i className="fa-solid fa-grid-2 mr-1"></i>Grid
                  </button>
                </div>
              </div>
            </div>

            {/* Filter Tags */}
            <div className="p-3 border-b border-gray-700 bg-gray-800/30">
              <div className="flex flex-wrap gap-1 mb-2">
                {/* Provider filters - show active ones */}
                {activeFilters.filter(f => !['flagship', 'free', 'reasoning', 'vision', 'local', 'favorites'].includes(f)).map(filter => (
                  <span key={filter} className="px-2 py-1 text-xs rounded-md border border-primary bg-primary/20 text-primary flex items-center gap-1">
                    {filter} <i className="fa-solid fa-xmark cursor-pointer hover:text-white" onClick={() => toggleFilter(filter)}></i>
                  </span>
                ))}
              </div>
              <div className="flex flex-wrap gap-1">
                <button
                  onClick={() => toggleFilter('flagship')}
                  className={`px-2 py-1 text-xs rounded-md border flex items-center gap-1 transition-colors ${
                    activeFilters.includes('flagship')
                      ? 'bg-primary text-gray-900 border-primary font-medium'
                      : 'border-gray-600 text-gray-300 hover:bg-primary/10 hover:border-primary hover:text-primary'
                  }`}
                >
                  <i className="fa-solid fa-ship text-xs"></i> Flagship
                </button>
                <button
                  onClick={() => toggleFilter('free')}
                  className={`px-2 py-1 text-xs rounded-md border flex items-center gap-1 transition-colors ${
                    activeFilters.includes('free')
                      ? 'bg-primary text-gray-900 border-primary font-medium'
                      : 'border-gray-600 text-gray-300 hover:bg-primary/10 hover:border-primary hover:text-primary'
                  }`}
                >
                  <i className="fa-solid fa-gift text-xs"></i> Free
                </button>
                {!isPrivateMode && (
                  <button
                    onClick={() => toggleFilter('local')}
                    className={`px-2 py-1 text-xs rounded-md border flex items-center gap-1 transition-colors ${
                      activeFilters.includes('local')
                        ? 'bg-primary text-gray-900 border-primary font-medium'
                        : 'border-gray-600 text-gray-300 hover:bg-primary/10 hover:border-primary hover:text-primary'
                    }`}
                  >
                    <i className="fa-solid fa-home text-xs"></i> Local
                  </button>
                )}
                <button
                  onClick={() => toggleFilter('reasoning')}
                  className={`px-2 py-1 text-xs rounded-md border flex items-center gap-1 transition-colors ${
                    activeFilters.includes('reasoning')
                      ? 'bg-primary text-gray-900 border-primary font-medium'
                      : 'border-gray-600 text-gray-300 hover:bg-primary/10 hover:border-primary hover:text-primary'
                  }`}
                >
                  <i className="fa-solid fa-brain text-xs"></i> Reasoning
                </button>
                <button
                  onClick={() => toggleFilter('vision')}
                  className={`px-2 py-1 text-xs rounded-md border flex items-center gap-1 transition-colors ${
                    activeFilters.includes('vision')
                      ? 'bg-primary text-gray-900 border-primary font-medium'
                      : 'border-gray-600 text-gray-300 hover:bg-primary/10 hover:border-primary hover:text-primary'
                  }`}
                >
                  <i className="fa-solid fa-eye text-xs"></i> Vision
                </button>
                <button
                  onClick={() => toggleFilter('favorites')}
                  className={`px-2 py-1 text-xs rounded-md border flex items-center gap-1 transition-colors ${
                    activeFilters.includes('favorites')
                      ? 'bg-primary text-gray-900 border-primary font-medium'
                      : 'border-gray-600 text-gray-300 hover:bg-primary/10 hover:border-primary hover:text-primary'
                  }`}
                >
                  <i className="fa-solid fa-star text-xs"></i> Favorites
                </button>
              </div>
            </div>

            {/* Model List/Grid */}
            <div className="flex-1 overflow-y-auto p-3">
              {!isModelsLoaded ? (
                /* Loading State */
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-3"></div>
                    <div className="text-sm text-gray-400">Loading models...</div>
                  </div>
                </div>
              ) : paginatedModels.length === 0 ? (
                /* No Results or Local Model Guidance */
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    {isPrivateMode && localModels.length === 0 ? (
                      /* Local Model Guidance */
                      <>
                        <i className="fa-solid fa-home text-3xl text-primary mb-3"></i>
                        <div className="text-sm text-supplement1 mb-2">No Local Models Found</div>
                        <div className="text-xs text-gray-400 mb-4 max-w-sm">
                          To use local models in private mode, please install and run:
                        </div>
                        <div className="space-y-2 mb-4">
                          <div className="flex items-center justify-center gap-2 text-xs">
                            <i className="fa-solid fa-external-link text-primary"></i>
                            <a
                              href="https://ollama.ai"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:text-white transition-colors"
                            >
                              Ollama
                            </a>
                            <span className="text-gray-500">or</span>
                            <a
                              href="https://lmstudio.ai"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:text-white transition-colors"
                            >
                              LM Studio
                            </a>
                          </div>
                        </div>
                        <button
                          onClick={() => window.location.reload()}
                          className="px-3 py-1 text-xs bg-primary text-gray-900 rounded hover:bg-primary/90 transition-colors"
                        >
                          <i className="fa-solid fa-refresh mr-1"></i>Refresh
                        </button>
                      </>
                    ) : (
                      /* Regular No Results */
                      <>
                        <i className="fa-solid fa-search text-3xl text-gray-500 mb-3"></i>
                        <div className="text-sm text-gray-400">No models found</div>
                        <div className="text-xs text-gray-500 mt-1">Try adjusting your search or filters</div>
                      </>
                    )}
                  </div>
                </div>
              ) : viewMode === 'list' ? (
                /* List View */
                <div className="space-y-1">
                  {paginatedModels.map((model) => {
                    const enhanced = enhanceModelInfo(model)
                    const isSelected = model.id === localSettings.selectedModel
                    const isFavorite = localSettings.favoriteModels?.includes(model.id)

                    return (
                      <div
                        key={model.id}
                        onClick={() => setLocalSettings({ ...localSettings, selectedModel: model.id })}
                        className={`p-2 border-b border-gray-700/50 cursor-pointer transition-all hover:bg-primary/5 ${
                          isSelected ? 'bg-primary/20 border-l-2 border-l-primary shadow-sm' : 'hover:border-l-2 hover:border-l-primary/50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                toggleFavorite(model.id)
                              }}
                              className="text-gray-400 hover:text-secondary transition-colors"
                            >
                              <i className={`fa-${isFavorite ? 'solid' : 'regular'} fa-star text-xs ${isFavorite ? 'text-secondary' : ''}`}></i>
                            </button>
                            <div>
                              <div className={`font-medium text-sm ${isSelected ? 'text-primary' : 'text-supplement1'}`}>{model.name}</div>
                              <div className="text-xs text-gray-400">
                                {enhanced.provider} • {formatContextLength(model.context_length)}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            {enhanced.isFlagship && <span className="px-1.5 py-0.5 bg-tertiary/20 text-supplement1 text-xs rounded">Flagship</span>}
                            {enhanced.isFree && <span className="px-1.5 py-0.5 bg-green-600/20 text-green-400 text-xs rounded">Free</span>}
                            {enhanced.isReasoning && <span className="px-1.5 py-0.5 bg-purple-600/20 text-purple-400 text-xs rounded">Reasoning</span>}
                            {enhanced.isVision && <span className="px-1.5 py-0.5 bg-blue-600/20 text-blue-400 text-xs rounded">Vision</span>}
                            {isSelected && <span className="px-2 py-0.5 bg-primary text-gray-900 text-xs rounded font-medium">Selected</span>}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                /* Grid View */
                <div className="grid grid-cols-2 gap-3">
                  {paginatedModels.map((model) => {
                    const enhanced = enhanceModelInfo(model)
                    const isSelected = model.id === localSettings.selectedModel
                    const isFavorite = localSettings.favoriteModels?.includes(model.id)

                    return (
                      <div
                        key={model.id}
                        onClick={() => setLocalSettings({ ...localSettings, selectedModel: model.id })}
                        className={`p-3 bg-gray-800/40 border rounded-lg cursor-pointer transition-all hover:bg-primary/5 ${
                          isSelected ? 'border-primary bg-primary/15 shadow-md' : 'border-gray-600 hover:border-primary/50'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                toggleFavorite(model.id)
                              }}
                              className="text-gray-400 hover:text-secondary transition-colors"
                            >
                              <i className={`fa-${isFavorite ? 'solid' : 'regular'} fa-star text-sm ${isFavorite ? 'text-secondary' : ''}`}></i>
                            </button>
                            <div>
                              <h3 className={`font-medium text-sm ${isSelected ? 'text-primary' : 'text-supplement1'}`}>{model.name}</h3>
                              <div className="text-xs text-gray-400">
                                {enhanced.provider} • {formatContextLength(model.context_length)}
                              </div>
                            </div>
                          </div>
                          {isSelected && <span className="px-2 py-1 bg-primary text-gray-900 text-xs rounded font-medium">Selected</span>}
                        </div>

                        {model.description && (
                          <p className="text-xs text-gray-300 leading-relaxed mb-3 line-clamp-2">
                            {model.description}
                          </p>
                        )}

                        {/* Model capabilities badges */}
                        <div className="flex flex-wrap gap-1 mb-3">
                          {enhanced.isFlagship && <span className="px-1.5 py-0.5 bg-tertiary/20 text-supplement1 text-xs rounded">Flagship</span>}
                          {enhanced.isFree && <span className="px-1.5 py-0.5 bg-green-600/20 text-green-400 text-xs rounded">Free</span>}
                          {enhanced.isReasoning && <span className="px-1.5 py-0.5 bg-purple-600/20 text-purple-400 text-xs rounded">Reasoning</span>}
                          {enhanced.isVision && <span className="px-1.5 py-0.5 bg-blue-600/20 text-blue-400 text-xs rounded">Vision</span>}
                        </div>

                        {/* Card footer with pricing */}
                        <div className="flex justify-between items-center pt-2 border-t border-gray-700/50">
                          <div className="text-xs text-gray-400">{formatPricing(model)}</div>
                          <button className="text-xs text-primary hover:text-white transition-colors">
                            <i className="fa-solid fa-info-circle mr-1"></i>Details
                          </button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}

              {/* Load More Button */}
              {isModelsLoaded && paginatedModels.length < filteredModels.length && (
                <div className="text-center py-4">
                  <button
                    onClick={() => {
                      const itemsPerPage = viewMode === 'grid' ? 20 : 50
                      const currentCount = paginatedModels.length
                      const newCount = Math.min(currentCount + itemsPerPage, filteredModels.length)
                      // This would need to be implemented with state management
                    }}
                    className="px-4 py-2 text-sm bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-md transition-colors"
                  >
                    Load More ({filteredModels.length - paginatedModels.length} remaining)
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Right Column: Settings */}
          <div className="w-2/5 flex flex-col">

            {/* Quick Presets (Top Right) */}
            <div className="p-3 border-b border-gray-700 bg-gray-800/30">
              <h3 className="text-sm font-medium text-supplement1 mb-2">Quick Presets</h3>
              <div className="flex gap-1">
                {modelPresets.map((preset) => (
                  <button
                    key={preset.name}
                    onClick={() => applyPreset(preset)}
                    className={`px-2 py-1 text-xs border rounded transition-colors ${
                      selectedPreset === preset.name
                        ? 'border-primary bg-primary/20 text-primary font-medium'
                        : 'border-gray-600 text-gray-300 hover:border-primary hover:bg-primary/10 hover:text-primary'
                    }`}
                  >
                    {preset.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Settings Content */}
            <div className="flex-1 p-4 overflow-y-auto space-y-6">

              {/* Advanced Settings (Interactive Cards) */}
              <div>
                <h3 className="text-sm font-medium text-supplement1 mb-2">Advanced Settings</h3>
                <div className="grid grid-cols-2 gap-2">
                  {/* Context Window - Read-only info */}
                  <div className="p-2 bg-gray-700/30 rounded border border-gray-600">
                    <div className="text-xs text-gray-400 mb-1">Context Window</div>
                    <div className="text-sm font-medium text-supplement1">
                      {localSettings.selectedModel ?
                        formatContextLength(availableModels.find(m => m.id === localSettings.selectedModel)?.context_length || 4096) :
                        '4K tokens'
                      }
                    </div>
                  </div>

                  {/* Response Format - Info only for now */}
                  <div className="p-2 bg-gray-700/30 rounded border border-gray-600">
                    <div className="text-xs text-gray-400 mb-1">Response Format</div>
                    <div className="text-sm font-medium text-supplement1">Text</div>
                  </div>

                  {/* Streaming - Info only for now */}
                  <div className="p-2 bg-gray-700/30 rounded border border-gray-600">
                    <div className="text-xs text-gray-400 mb-1">Streaming</div>
                    <div className="text-sm font-medium text-supplement1">Enabled</div>
                  </div>

                  {/* Safety Filter - Info only for now */}
                  <div className="p-2 bg-gray-700/30 rounded border border-gray-600">
                    <div className="text-xs text-gray-400 mb-1">Safety Filter</div>
                    <div className="text-sm font-medium text-supplement1">Standard</div>
                  </div>
                </div>
              </div>

              {/* System Prompt */}
              <div>
                <h3 className="text-sm font-semibold text-supplement1 mb-3">System Prompt</h3>
                <textarea
                  value={localSettings.systemPrompt || ''}
                  onChange={(e) => setLocalSettings({ ...localSettings, systemPrompt: e.target.value })}
                  placeholder="Enter system prompt to define AI behavior..."
                  className="w-full h-24 p-3 bg-gray-700 border border-gray-600 rounded-lg text-sm resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Parameters */}
              <div>
                <h3 className="text-sm font-semibold text-supplement1 mb-3">Parameters</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-400">Temperature</span>
                      <span className="text-supplement1">{localSettings.temperature?.toFixed(1) || '0.7'}</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="2"
                      step="0.1"
                      value={localSettings.temperature || 0.7}
                      onChange={(e) => setLocalSettings({ ...localSettings, temperature: parseFloat(e.target.value) })}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-primary"
                    />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-400">Top-P</span>
                      <span className="text-supplement1">{localSettings.topP?.toFixed(2) || '0.95'}</span>
                    </div>
                    <input
                      type="range"
                      min="0.1"
                      max="1"
                      step="0.05"
                      value={localSettings.topP || 0.95}
                      onChange={(e) => setLocalSettings({ ...localSettings, topP: parseFloat(e.target.value) })}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-primary"
                    />
                  </div>
                </div>
              </div>

              {/* Max Tokens */}
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-400">Max Tokens</span>
                  <span className="text-supplement1">{localSettings.maxTokens?.toLocaleString() || '4,096'}</span>
                </div>
                <input
                  type="range"
                  min="256"
                  max="100000"
                  step="256"
                  value={localSettings.maxTokens || 4096}
                  onChange={(e) => setLocalSettings({ ...localSettings, maxTokens: parseInt(e.target.value) })}
                  className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-primary"
                />
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-700 flex justify-end gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-400 hover:text-supplement1 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-primary text-gray-900 rounded-md hover:bg-primary/90 transition-colors font-medium"
              >
                Save Settings
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Local Model Toast Notification */}
      {showLocalModelToast && (
        <div className="fixed bottom-4 right-4 bg-gray-800 border border-primary rounded-lg p-4 shadow-lg z-[10000] max-w-sm">
          <div className="flex items-start gap-3">
            <i className="fa-solid fa-info-circle text-primary mt-0.5"></i>
            <div className="flex-1">
              <div className="text-sm font-medium text-supplement1 mb-1">Local Models Required</div>
              <div className="text-xs text-gray-400 mb-3">
                Install Ollama or LM Studio to use local models in private mode.
              </div>
              <div className="flex gap-2">
                <a
                  href="https://ollama.ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-2 py-1 text-xs bg-primary text-gray-900 rounded hover:bg-primary/90 transition-colors"
                >
                  Get Ollama
                </a>
                <button
                  onClick={() => setShowLocalModelToast(false)}
                  className="px-2 py-1 text-xs border border-gray-600 text-gray-400 rounded hover:bg-gray-700 transition-colors"
                >
                  Dismiss
                </button>
              </div>
            </div>
            <button
              onClick={() => setShowLocalModelToast(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <i className="fa-solid fa-xmark text-xs"></i>
            </button>
          </div>
        </div>
      )}
    </div>
  )

  return createPortal(modalContent, document.body)
}

export default ChatSettingsDrawer
